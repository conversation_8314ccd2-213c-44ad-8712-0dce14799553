// app/lib/ai/registry.ts
// AI provider registry and configuration

import { createOpenAI, openai } from "@ai-sdk/openai";
import { createReplicate, replicate } from "@ai-sdk/replicate";

import type { AiRequestOptions, KlingProviderConfig } from "./types";

// Lazy provider initialization to avoid global scope access
let openaiProvider: ReturnType<typeof createOpenAI> | null = null;

function getOpenAIProvider(apiKey?: string) {
  if (!openaiProvider) {
    openaiProvider = createOpenAI({
      apiKey: apiKey || "",
    });
  }
  return openaiProvider;
}

// Provider configuration - now using lazy initialization
export function getProviders(env?: { OPENAI_API_KEY?: string }) {
  return {
    openai: getOpenAIProvider(env?.OPENAI_API_KEY),
    replicate,
  } as const;
}

// Helper functions to get models
export function getOpenAIModel(modelId: string, env?: { OPENAI_API_KEY?: string }) {
  // For simple cases, use the static openai function
  // For provider-specific config, use getOpenAIProvider
  return openai(modelId);
}

export function getReplicateModel(modelId: string) {
  // Note: For actual usage, prefer using createAIModel from ai-providers.ts
  // This function is mainly for reference and simple cases
  // The correct implementation is in app/lib/ai/ai-providers.ts
  throw new Error("Use createAIModel from ai-providers.ts for Replicate models");
}

// Generic model getter
export function getModel(providerId: string, modelId: string, env?: { OPENAI_API_KEY?: string }) {
  switch (providerId) {
    case "openai":
      return getOpenAIModel(modelId, env);
    case "replicate":
      return getReplicateModel(modelId);
    default:
      throw new Error(`Unknown provider: ${providerId}`);
  }
}

// Available models configuration
export const availableModels = {
  openai: {
    text: ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4o", "gpt-4o-mini"] as const,
    image: ["dall-e-3", "dall-e-2"] as const,
  },
  replicate: {
    text: ["meta/llama-2-70b-chat", "mistralai/mixtral-8x7b-instruct-v0.1"] as const,
    image: ["stability-ai/stable-diffusion", "black-forest-labs/flux-schnell"] as const,
  },
} as const;

type ProviderName = keyof typeof availableModels;
type ModelType = "text" | "image";

// Helper to check if a model is available
export function isModelAvailable(
  providerId: ProviderName,
  modelId: string,
  type: ModelType = "text"
): boolean {
  const provider = availableModels[providerId];
  if (!provider) return false;

  const models = provider[type];
  return models.includes(modelId as never);
}

// Helper to get all available models for a provider
export function getAvailableModels(providerId: ProviderName, type?: ModelType) {
  const provider = availableModels[providerId];
  if (!provider) return [];

  if (type) {
    return provider[type] || [];
  }

  return {
    text: provider.text || [],
    image: provider.image || [],
  };
}

console.log("AI Provider Registry initialized with: openai, replicate");
