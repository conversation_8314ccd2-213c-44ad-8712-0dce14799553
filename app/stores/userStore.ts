// app/stores/userStore.ts
import { create } from "zustand";
import { createJSONStorage, devtools, persist } from "zustand/middleware";
import type { User, UserState } from "./types";

const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

export const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        setUser: (user: User) => {
          set(
            {
              user,
              isAuthenticated: true,
              error: null,
            },
            false,
            "setUser"
          );
        },

        clearUser: () => {
          set(
            {
              user: null,
              isAuthenticated: false,
              error: null,
            },
            false,
            "clearUser"
          );
        },

        setLoading: (isLoading: boolean) => {
          set({ isLoading }, false, "setLoading");
        },

        setError: (error: string | null) => {
          set({ error }, false, "setError");
        },

        reset: () => {
          set(initialState, false, "reset");
        },
      }),
      {
        name: "user-store",
        storage: createJSONStorage(() => {
          if (typeof window !== "undefined") {
            return localStorage;
          }
          // Return a no-op storage for server-side
          return {
            getItem: () => null,
            setItem: () => {},
            removeItem: () => {},
          };
        }),
        // 只持久化用户信息，不持久化 loading 和 error 状态
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
        }),
        // 在服务端渲染时跳过持久化
        skipHydration: typeof window === "undefined",
      }
    ),
    {
      name: "user-store",
    }
  )
);

// 选择器函数，用于优化性能
export const selectUser = (state: UserState) => state.user;
export const selectIsAuthenticated = (state: UserState) => state.isAuthenticated;
export const selectIsLoading = (state: UserState) => state.isLoading;
export const selectError = (state: UserState) => state.error;

// 便捷的 hooks
export const useUser = () => useUserStore(selectUser);
export const useIsAuthenticated = () => useUserStore(selectIsAuthenticated);
export const useUserLoading = () => useUserStore(selectIsLoading);
export const useUserError = () => useUserStore(selectError);

// Actions hooks
export const useUserActions = () => {
  const setUser = useUserStore((state) => state.setUser);
  const clearUser = useUserStore((state) => state.clearUser);
  const setLoading = useUserStore((state) => state.setLoading);
  const setError = useUserStore((state) => state.setError);
  const reset = useUserStore((state) => state.reset);

  return {
    setUser,
    clearUser,
    setLoading,
    setError,
    reset,
  };
};
