import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { createDbFromEnv, dbOperations, dbQueries } from "~/lib/db";
import { findUserByUuid } from "~/models/user";
import { getUserUuid } from "~/services/user";

// Type definitions for request body
interface AdminStatsBody {
  dateFrom?: string;
  dateTo?: string;
  accountId?: string;
  includeUserStats?: boolean;
  includeOrderBreakdown?: boolean;
}

// Simple admin check - in a real app, you'd have proper role-based access control
async function isAdmin(context: any): Promise<boolean> {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);
    const userUuid = await getUserUuid();

    if (!userUuid || !db) return false;

    const user = await findUserByUuid(userUuid, db);
    // For now, check if user is an affiliate (you can enhance this with proper admin roles)
    return user?.isAffiliate === true;
  } catch (error) {
    console.error("Admin check failed:", error);
    return false;
  }
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Check admin permissions
    const isUserAdmin = await isAdmin(context);
    if (!isUserAdmin) {
      return json(
        { success: false, error: "Unauthorized - Admin access required" },
        { status: 403 }
      );
    }

    const db = context.db || createDbFromEnv(context.cloudflare?.env);
    if (!db) {
      return json({ success: false, error: "Database connection failed" }, { status: 500 });
    }

    // Parse query parameters for date range
    const url = new URL(request.url);
    const daysBack = parseInt(url.searchParams.get("days") || "30");
    const dateFrom = new Date();
    dateFrom.setDate(dateFrom.getDate() - daysBack);

    // Get comprehensive system statistics
    const [dashboardOverview, orderAnalytics, dbHealth, dbStats] = await Promise.allSettled([
      dbQueries.dashboard.getOverview(db),
      dbQueries.order.getOrderAnalytics(db, { dateFrom }),
      dbOperations.health.checkConnection(db),
      dbOperations.health.getStats(db),
    ]);

    // Compile statistics
    const stats = {
      timestamp: new Date().toISOString(),
      period: {
        days: daysBack,
        from: dateFrom.toISOString(),
        to: new Date().toISOString(),
      },
      overview: dashboardOverview.status === "fulfilled" ? dashboardOverview.value : null,
      analytics: orderAnalytics.status === "fulfilled" ? orderAnalytics.value : null,
      system: {
        database: {
          healthy: dbHealth.status === "fulfilled" ? dbHealth.value : false,
          statistics: dbStats.status === "fulfilled" ? dbStats.value : null,
        },
        memory:
          typeof performance !== "undefined" && performance.memory
            ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024), // MB
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024), // MB
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024), // MB
                usage: Math.round(
                  (performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize) * 100
                ), // %
              }
            : null,
      },
      errors: {
        dashboardOverview:
          dashboardOverview.status === "rejected" ? dashboardOverview.reason?.message : null,
        orderAnalytics:
          orderAnalytics.status === "rejected" ? orderAnalytics.reason?.message : null,
        dbHealth: dbHealth.status === "rejected" ? dbHealth.reason?.message : null,
        dbStats: dbStats.status === "rejected" ? dbStats.reason?.message : null,
      },
    };

    return json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error("Admin stats failed:", error);
    return json(
      {
        success: false,
        error: "Failed to get admin statistics",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// POST method for more complex analytics queries
export async function action({ request, context }: LoaderFunctionArgs) {
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Check admin permissions
    const isUserAdmin = await isAdmin(context);
    if (!isUserAdmin) {
      return json(
        { success: false, error: "Unauthorized - Admin access required" },
        { status: 403 }
      );
    }

    const db = context.db || createDbFromEnv(context.cloudflare?.env);
    if (!db) {
      return json({ success: false, error: "Database connection failed" }, { status: 500 });
    }

    // Parse request body
    const body = (await request.json()) as AdminStatsBody;
    const {
      dateFrom,
      dateTo,
      accountId,
      includeUserStats = false,
      includeOrderBreakdown = false,
    } = body;

    const analytics: any = {};

    // Get order analytics with custom date range
    if (dateFrom || dateTo || accountId) {
      const orderAnalytics = await dbQueries.order.getOrderAnalytics(db, {
        dateFrom: dateFrom ? new Date(dateFrom) : undefined,
        dateTo: dateTo ? new Date(dateTo) : undefined,
        accountId,
      });
      analytics.orders = orderAnalytics;
    }

    // Get user statistics if requested
    if (includeUserStats) {
      try {
        const userStats = await dbQueries.user.searchUsers(db, {
          page: 1,
          limit: 1, // Just get the count
        });

        // Get affiliate breakdown
        const affiliateStats = await dbQueries.user.searchUsers(db, {
          page: 1,
          limit: 1,
          isAffiliate: true,
        });

        analytics.users = {
          total: userStats.pagination.total,
          affiliates: affiliateStats.pagination.total,
          regular: userStats.pagination.total - affiliateStats.pagination.total,
        };
      } catch (error) {
        analytics.users = {
          error: error instanceof Error ? error.message : "Failed to get user stats",
        };
      }
    }

    // Get detailed order breakdown if requested
    if (includeOrderBreakdown) {
      try {
        const orderBreakdown = await dbQueries.order.searchOrders(db, {
          page: 1,
          limit: 100, // Get recent orders for analysis
          dateFrom: dateFrom ? new Date(dateFrom) : undefined,
          dateTo: dateTo ? new Date(dateTo) : undefined,
        });

        analytics.orderBreakdown = {
          recent: orderBreakdown.data,
          pagination: orderBreakdown.pagination,
        };
      } catch (error) {
        analytics.orderBreakdown = {
          error: error instanceof Error ? error.message : "Failed to get order breakdown",
        };
      }
    }

    return json({
      success: true,
      data: {
        timestamp: new Date().toISOString(),
        filters: { dateFrom, dateTo, accountId },
        analytics,
      },
    });
  } catch (error) {
    console.error("Custom admin analytics failed:", error);
    return json(
      {
        success: false,
        error: "Failed to get custom analytics",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
