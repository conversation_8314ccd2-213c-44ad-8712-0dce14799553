// import { openai } from "@ai-sdk/openai"; // Removed - using direct API calls
import { replicate } from "@ai-sdk/replicate";
import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { generateObject } from "ai";
import { z } from "zod";
import {
  AI_ERROR_MESSAGES,
  getAIEnvironmentVariables,
  getAIErrorMessage,
  logAIOperation,
  sanitizePromptForLogging,
} from "~/lib/ai/ai-utils";
import { CLOUDFLARE_AI_MODELS, CloudflareAIService } from "~/lib/ai/cloudflare-ai"; // Imported CLOUDFLARE_AI_MODELS
import { respData, respErr } from "~/lib/api/resp";
import { requireUser } from "~/lib/auth/middleware.server";
import { type ImageMetadata, ImageStorageService } from "~/lib/storage/image-storage.server.ts";
import { CreditsAmount, CreditsTransType, decreaseCredits } from "~/services/credit";

/**
 * Image generation providers
 */
export type ImageProvider = "openai" | "replicate" | "cloudflare";

/**
 * Image generation request schema
 */
const ImageGenerationSchema = z.object({
  prompt: z.string().min(1, "Prompt is required"),
  provider: z.enum(["openai", "replicate", "cloudflare"]),
  model: z.string().optional(),
  size: z.enum(["256x256", "512x512", "1024x1024", "1792x1024", "1024x1792"]).optional(),
  quality: z.enum(["standard", "hd"]).optional(),
  style: z.enum(["vivid", "natural"]).optional(),
  n: z.number().min(1).max(4).optional(),
});

/**
 * Image generation models configuration
 */
const IMAGE_MODELS = {
  openai: {
    "dall-e-2": {
      sizes: ["256x256", "512x512", "1024x1024"],
      maxImages: 10,
    },
    "dall-e-3": {
      sizes: ["1024x1024", "1792x1024", "1024x1792"],
      maxImages: 1,
      supportsQuality: true,
      supportsStyle: true,
    },
  },
  replicate: {
    "stability-ai/stable-diffusion": {
      sizes: ["512x512", "1024x1024"],
      maxImages: 4,
    },
  },
  cloudflare: {
    "@cf/stabilityai/stable-diffusion-xl-base-1.0": {
      sizes: ["1024x1024"], // Assuming fixed size for now
      maxImages: 1,
    },
  },
} as const;

async function fetchAndStoreImage(
  externalUrl: string,
  imageService: ImageStorageService,
  userId: string,
  prompt: string,
  index = 0 // For multiple images from a single prompt
): Promise<{
  key: string;
  url: string;
  originalUrl: string;
  size: number;
  contentType: string;
  etag?: string;
  customMetadata?: Record<string, string>;
} | null> {
  try {
    const response = await fetch(externalUrl);
    if (!response.ok) {
      console.error(
        `Failed to fetch image from external URL ${externalUrl}: ${response.statusText}`
      );
      return null;
    }
    const arrayBuffer = await response.arrayBuffer();
    const contentType = response.headers.get("content-type") || "image/png"; // Default or derive
    const originalFilename =
      externalUrl.substring(externalUrl.lastIndexOf("/") + 1).split("?")[0] ||
      `generated_image_${index}.png`;

    const blob = new Blob([arrayBuffer], { type: contentType });
    const imageFile = new File([blob], originalFilename, { type: contentType });

    const metadata: Partial<ImageMetadata> = {
      originalName: originalFilename,
      contentType: imageFile.type,
      size: imageFile.size,
      uploaderId: userId,
      generatedFromPrompt: prompt,
      externalSourceUrl: externalUrl,
    };

    const uploadedImage = await imageService.uploadImage(imageFile, metadata);
    if (!uploadedImage) {
      console.error(`Failed to upload image from ${externalUrl} to R2.`);
      return null;
    }

    return {
      key: uploadedImage.key,
      url: imageService.getImageUrl(uploadedImage.key),
      originalUrl: externalUrl,
      size: uploadedImage.size,
      contentType: uploadedImage.httpMetadata?.contentType || imageFile.type,
      etag: uploadedImage.etag,
      customMetadata: uploadedImage.customMetadata as Record<string, string>,
    };
  } catch (error) {
    console.error(`Error processing image from ${externalUrl}:`, error);
    return null;
  }
}

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  const startTime = Date.now();
  let provider: ImageProvider | undefined;
  let model: string | undefined;

  try {
    const db = context.db;
    if (!db) {
      return respErr("Database not available");
    }
    // Parse and validate request body
    const body = await request.json();
    const validationResult = ImageGenerationSchema.safeParse(body);

    if (!validationResult.success) {
      return respErr(
        `Invalid parameters: ${validationResult.error.errors.map((e) => e.message).join(", ")}`
      );
    }

    const {
      prompt,
      provider: reqProvider,
      model: reqModel = getDefaultModel(reqProvider),
      size = "1024x1024",
      quality = "standard",
      style = "vivid",
      n = 1,
    } = validationResult.data;

    provider = reqProvider;
    model = reqModel;

    // Check user authentication
    const user = await requireUser(request);

    // TODO: Implement rate limiting here
    // This could involve checking user's recent image generation requests
    // against a defined limit (e.g., X requests per Y minutes).
    // If rate limit is exceeded, return an appropriate error response (e.g., HTTP 429).
    // Example:
    // const isRateLimited = await checkUserRateLimit(user.id, "imageGeneration");
    // if (isRateLimited) {
    //   return respErr("Too many image generation requests. Please try again later.", 429);
    // }

    // Log the operation start
    console.log("Starting AI image generation:", {
      provider,
      model,
      prompt: sanitizePromptForLogging(prompt),
      size,
      quality,
      style,
      n,
      user_uuid: user.id,
      timestamp: new Date().toISOString(),
    });

    // Get environment variables
    const env = getAIEnvironmentVariables(context);

    const R2_BUCKET = env.R2_BUCKET;
    if (!R2_BUCKET) {
      console.error("R2_BUCKET environment variable is not set in action context.");
      return respErr(AI_ERROR_MESSAGES.CONFIG_ERROR, 500);
    }
    const R2_PUBLIC_URL = env.R2_PUBLIC_URL as string | undefined;
    const imageService = new ImageStorageService(R2_BUCKET, R2_PUBLIC_URL);

    // Generate image based on provider
    let imageUrls: string[] = [];

    if (provider === "openai") {
      imageUrls = await generateWithOpenAI({
        prompt,
        model,
        size,
        quality,
        style,
        n,
        env,
      });
    } else if (provider === "replicate") {
      imageUrls = await generateWithReplicate({
        prompt,
        model,
        size,
        n,
        env,
      });
    } else if (provider === "cloudflare") {
      if (!context.env.AI) {
        throw new Error(AI_ERROR_MESSAGES.cloudflareWorkersAIUnavailable);
      }
      const cloudflareAIService = new CloudflareAIService(context.env.AI);

      // Find the model key corresponding to the model ID
      const modelKey = Object.keys(CLOUDFLARE_AI_MODELS.imageGeneration).find(
        (key) =>
          CLOUDFLARE_AI_MODELS.imageGeneration[
            key as keyof CloudflareAIModels["imageGeneration"]
          ] === model
      ) as keyof CloudflareAIModels["imageGeneration"] | undefined;

      if (!modelKey) {
        throw new Error(`Unsupported Cloudflare image model: ${model}`);
      }

      const imageBuffer = await cloudflareAIService.generateImage(modelKey, { prompt });
      // Convert Uint8Array to base64 data URL
      const base64String = btoa(String.fromCharCode(...new Uint8Array(imageBuffer)));
      imageUrls = [`data:image/png;base64,${base64String}`];
    } else {
      return respErr("Unsupported image generation provider");
    }

    // Decrease user credits after successful generation
    try {
      const creditCost = CreditsAmount.AIImageGenerationCost * n;
      await decreaseCredits(
        {
          user_uuid: user.id,
          trans_type: CreditsTransType.AIImageGeneration,
          credits: creditCost,
        },
        db
      );
    } catch (creditError) {
      console.error("Failed to decrease credits:", creditError);
      // Continue with the response even if credit deduction fails
    }

    // Log successful operation
    logAIOperation("generate-image", provider, model, true, Date.now() - startTime);

    const storedImages = [];
    if (imageUrls.length > 0) {
      const userId = user.id;

      let idx = 0;
      for (const externalUrl of imageUrls) {
        const storedImage = await fetchAndStoreImage(
          externalUrl,
          imageService,
          userId,
          prompt,
          idx++
        );
        if (storedImage) {
          storedImages.push(storedImage);
        } else {
          // Handle failure to store an image, e.g., log or add error info
          storedImages.push({ error: "Failed to store image", originalUrl: externalUrl });
        }
      }
    }

    // Return successful response
    return respData({
      images: storedImages, // Array of objects with key, url, originalUrl, etc.
      provider,
      model,
      parameters: {
        size,
        quality,
        style,
        n,
      },
    });
  } catch (error) {
    // Log failed operation
    if (provider && model) {
      logAIOperation("generate-image", provider, model, false, Date.now() - startTime, error);
    }

    console.error("AI image generation failed:", {
      error: error.message || error,
      provider,
      model,
      stack: error.stack,
    });

    // Return user-friendly error message
    const errorMessage = getAIErrorMessage(error);
    return respErr(errorMessage);
  }
}

/**
 * Generate image with OpenAI DALL-E
 */
async function generateWithOpenAI({
  prompt,
  model,
  size,
  quality,
  style,
  n,
  env,
}: {
  prompt: string;
  model: string;
  size: string;
  quality: string;
  style: string;
  n: number;
  env: any;
}): Promise<string[]> {
  if (!env.OPENAI_API_KEY) {
    throw new Error("OPENAI_API_KEY is required for OpenAI image generation");
  }

  // OpenAI client setup - we'll use direct API calls for image generation

  // Use OpenAI's image generation API directly
  const response = await fetch("https://api.openai.com/v1/images/generations", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${env.OPENAI_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model,
      prompt,
      size,
      quality: model === "dall-e-3" ? quality : undefined,
      style: model === "dall-e-3" ? style : undefined,
      n: model === "dall-e-3" ? 1 : n, // DALL-E 3 only supports n=1
      response_format: "url",
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error?.message || "OpenAI image generation failed");
  }

  const result = await response.json();
  return result.data.map((item: any) => item.url);
}

/**
 * Generate image with Replicate
 */
async function generateWithReplicate({
  prompt,
  model,
  size,
  n,
  env,
}: {
  prompt: string;
  model: string;
  size: string;
  n: number;
  env: any;
}): Promise<string[]> {
  if (!env.REPLICATE_API_TOKEN) {
    throw new Error("REPLICATE_API_TOKEN is required for Replicate image generation");
  }

  // This is a simplified implementation
  // In a real scenario, you'd use the Replicate API directly
  throw new Error("Replicate image generation not yet implemented");
}

/**
 * Get default model for provider
 */
function getDefaultModel(provider: ImageProvider): string {
  switch (provider) {
    case "openai":
      return "dall-e-3";
    case "replicate":
      return "stability-ai/stable-diffusion";
    case "cloudflare":
      return "@cf/stabilityai/stable-diffusion-xl-base-1.0";
    default:
      throw new Error(`Unknown provider: ${provider}`);
  }
}
