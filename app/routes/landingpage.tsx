import { json, type LoaderFunctionArgs, type MetaFunction } from "@remix-run/cloudflare";
import { useLoaderData } from "@remix-run/react";
import Benefits from "~/components/blocks/benefits";
import Branding from "~/components/blocks/branding";
import CTA from "~/components/blocks/cta";
import FAQ from "~/components/blocks/faq";
import FeatureShowcase from "~/components/blocks/feature-showcase";
import Hero from "~/components/blocks/hero";
import Introduce from "~/components/blocks/introduce";
import Pricing from "~/components/blocks/pricing";
import Showcase from "~/components/blocks/showcase";
import Stats from "~/components/blocks/stats";
import Testimonials from "~/components/blocks/testimonials";
import UsageSteps from "~/components/blocks/usage-steps";
import UnifiedLayout from "~/components/layout/unified-layout";
import { getLandingPageConfig } from "~/config/landing";
import { getWebsiteStructuredData, siteConfig } from "~/config/seo";
import { generateStructuredData } from "~/lib/seo/seo";

export const meta: MetaFunction = () => {
  const title = "Landing Page - AI SaaS Starter";
  const description =
    "Complete landing page showcase with all components - Hero, Features, Pricing, and more.";

  return [
    { title },
    { name: "description", content: description },
    { property: "og:title", content: title },
    { property: "og:description", content: description },
    { property: "og:type", content: "website" },
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:title", content: title },
    { name: "twitter:description", content: description },
    {
      "script:ld+json": generateStructuredData({
        type: "WebSite",
        name: title,
        description,
        url: `${siteConfig.url}/landingpage`,
      }),
    },
  ];
};

export async function loader({ context }: LoaderFunctionArgs) {
  const config = getLandingPageConfig();

  return json({
    config,
  });
}

export default function LandingPage() {
  const { config } = useLoaderData<typeof loader>();

  return (
    <UnifiedLayout showHeader={true} showFooter={true} showSidebar={false} containerSize="full">
      {/* Hero Section */}
      <Hero
        title={config.hero.title}
        description={config.hero.description}
        highlight_text={config.hero.highlight_text}
        buttons={config.hero.buttons}
        announcement={config.hero.announcement}
        tip={config.hero.tip}
        show_badge={config.hero.show_badge}
        show_happy_users={config.hero.show_happy_users}
      />

      {/* Branding Section */}
      <Branding
        title={config.branding.title}
        description={config.branding.description}
        items={config.branding.items}
      />

      {/* Introduce Section */}
      <Introduce
        title={config.introduce.title}
        description={config.introduce.description}
        content={config.introduce.content}
        image={config.introduce.image}
        buttons={config.introduce.buttons}
        features={config.introduce.features}
        reversed={config.introduce.reversed}
      />

      {/* Benefits Section */}
      <Benefits
        title={config.benefits.title}
        description={config.benefits.description}
        items={config.benefits.items}
      />

      {/* Usage/Feature Section */}
      <UsageSteps
        title={config.usage.title}
        description={config.usage.description}
        items={config.usage.items}
      />

      {/* Feature Showcase */}
      <FeatureShowcase
        title={config.features.title}
        description={config.features.description}
        items={config.features.items}
      />

      {/* Showcase Section */}
      <Showcase title={config.showcase.title} items={config.showcase.items} />

      {/* Stats Section */}
      <Stats
        title={config.stats.title}
        description={config.stats.description}
        items={config.stats.items}
      />

      {/* Pricing Section */}
      <Pricing />

      {/* Testimonials Section */}
      <Testimonials
        title={config.testimonials.title}
        description={config.testimonials.description}
        items={config.testimonials.items}
      />

      {/* FAQ Section */}
      <FAQ title={config.faq.title} description={config.faq.description} items={config.faq.items} />

      {/* CTA Section */}
      <CTA
        title={config.cta.title}
        description={config.cta.description}
        buttons={config.cta.buttons}
      />
    </UnifiedLayout>
  );
}
